/*-- js逻辑部分 --*/

//格式化--时间
export function timeStr(dataStr) {
	var date = new Date(dataStr);
	var y = date.getFullYear();

	var m = date.getMonth() + 1;
	m = m < 10 ? ('0' + m) : m;

	var d = date.getDate();
	d = d < 10 ? ('0' + d) : d;

	var h = date.getHours();
	h = h < 10 ? ('0' + h) : h;

	//获得分
	var mm = date.getMinutes()
	mm = mm < 10 ? ('0' + mm) : mm;

	//获得秒
	var ss = date.getSeconds()
	ss = ss < 10 ? ('0' + ss) : ss;

	// console.log(y+'-'+m+'-'+d+' '+h+':'+mm+':'+ss)

	return y + '-' + m + '-' + d + ' ' + h + ':' + mm + ':' + ss
}

export function dateStr(dataStr) {
	var date = new Date(dataStr);
	var y = date.getFullYear();

	var m = date.getMonth() + 1;
	m = m < 10 ? ('0' + m) : m;

	var d = date.getDate();
	d = d < 10 ? ('0' + d) : d;

	// console.log(y+'-'+m+'-'+d+' '+h+':'+mm+':'+ss)

	return y + '-' + m + '-' + d
}


export function monthStr(dataStr) {
	var date = new Date(dataStr);
	var y = date.getFullYear();

	var m = date.getMonth() + 1;
	m = m < 10 ? ('0' + m) : m;

	// console.log(y+'-'+m+'-'+d+' '+h+':'+mm+':'+ss)

	return y + '-' + m
}


export function hourStr(dataStr) {
	var date = new Date(dataStr);
	var h = date.getHours();
	h = h < 10 ? ('0' + h) : h;

	//获得分
	var mm = date.getMinutes()
	mm = mm < 10 ? ('0' + mm) : mm;

	//获得秒
	var ss = date.getSeconds()
	ss = ss < 10 ? ('0' + ss) : ss;

	// console.log(y+'-'+m+'-'+d+' '+h+':'+mm+':'+ss)

	return h + ':' + mm + ':' + ss
}


//时分秒转换为时间戳
export function time_to_sec(time) {
	if (time !== null) {
		var s = "";
		var hour = time.split(":")[0];
		var min = time.split(":")[1];
		var sec = time.split(":")[2];
		s = Number(hour * 3600 * 1000) + Number(min * 60 * 1000) + Number(sec * 1000);
		return s;
	}
}

//获取每月第一天
export function getMonthFirst(val) {
	var date = new Date(val)
	date.setDate(1)
	var month = parseInt(date.getMonth() + 1)
	var day = date.getDate()
	if (month < 10) {
		month = '0' + month
	}
	if (day < 10) {
		day = '0' + day
	}
	return date.getFullYear() + '-' + month + '-' + day + ' ' + "00" + ':' + "00" + ':' + "00"
}

//获取每月最后一天
export function getMonthLast(val) {
	var date = new Date(val)
	var currentMonth = date.getMonth()
	var nextMonth = ++currentMonth
	var nextMonthFirstDay = new Date(date.getFullYear(), nextMonth, 1)
	var oneDay = 1000 * 60 * 60 * 24
	var lastTime = new Date(nextMonthFirstDay - oneDay)
	var month = parseInt(lastTime.getMonth() + 1)
	var day = lastTime.getDate()
	if (month < 10) {
		month = '0' + month
	}
	if (day < 10) {
		day = '0' + day
	}
	return date.getFullYear() + '-' + month + '-' + day + ' ' + "23" + ':' + "59" + ':' + "59"
}


// 查询月份开始结束
//开始日期
export function getFirstDayOfMonth(year, month) {
	// return new Date(year, month-1, 1);
	let data = new Date(year, month - 1, 1);
	return (
		data.getFullYear() +
		"-" +
		(data.getMonth() + 1 > 9 ?
			data.getMonth() + 1 :
			"0" + (data.getMonth() + 1)) +
		"-" +
		(data.getDate() > 9 ? data.getDate() : "0" + data.getDate())
	);
}
//结束日期
export function getLastDayOfMonth(year, month) {
	let data = new Date(year, month, 0);
	return (
		data.getFullYear() +
		"-" +
		(data.getMonth() + 1 > 9 ?
			data.getMonth() + 1 :
			"0" + (data.getMonth() + 1)) +
		"-" +
		(data.getDate() > 9 ? data.getDate() : "0" + data.getDate())
	);
}

//获取两个日期间的每一天
export function formatEveryDay(start, end) {
	let dateList = [];
	var startTime = getDate(start);
	var endTime = getDate(end);

	while ((endTime.getTime() - startTime.getTime()) >= 0) {
		var year = startTime.getFullYear();
		var month = startTime.getMonth() + 1 < 10 ? '0' + (startTime.getMonth() + 1) : startTime.getMonth() + 1;
		var day = startTime.getDate().toString().length == 1 ? "0" + startTime.getDate() : startTime.getDate();
		dateList.push(year + "-" + month + "-" + day);
		startTime.setDate(startTime.getDate() + 1);
	}
	return dateList;
}

function getDate(datestr) {
	var temp = datestr.split("-");
	var date = new Date(temp[0], temp[1] - 1, temp[2]);
	return date;
}
