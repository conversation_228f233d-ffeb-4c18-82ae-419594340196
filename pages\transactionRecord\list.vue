<template>
	<view class="transaction-list">
		<!-- 交易记录列表 -->
		<view class="record-section">
			<view class="record-header">
				<text class="record-title">交易记录</text>
				<text class="record-count">共{{ transactionList.length }}条记录</text>
			</view>
			
			<view class="record-list" v-if="transactionList.length > 0">
				<view class="record-item" v-for="(item, index) in transactionList" :key="index" @click="goToDetail(item)">
					<view class="record-main">
						<view class="record-info">
							<view class="record-device">
								<text class="device-text">设备编号：{{ item.deviceNumber }}</text>
							</view>
							<view class="record-time">
								<text class="time-text">{{ formatTime(item.transactionTime) }}</text>
							</view>
							<view class="record-user" v-if="item.userName">
								<text class="user-text">{{ item.userName }}</text>
							</view>
						</view>
						<view class="record-amount">
							<text class="amount-value" :class="{'amount-income': item.type === 'recharge', 'amount-expense': item.type === 'consume', 'amount-refund': item.type === 'refund'}">
								{{ item.type === 'recharge' ? '+' : '-' }}¥{{ item.amount }}
							</text>
							<text class="amount-type">{{ item.typeName }}</text>
						</view>
					</view>
					<view class="record-summary">
						<text class="summary-text">余额：¥{{ item.balance }}</text>
						<text class="arrow-icon">›</text>
					</view>
				</view>
			</view>
			
			<view class="empty-state" v-else>
				<text class="iconfont">&#xe708;</text>
				<text class="empty-text">暂无交易记录</text>
				<text class="empty-tip">请调整查询条件后重试</text>
			</view>
		</view>

		<!-- 加载更多 -->
		<view class="load-more" v-if="transactionList.length > 0 && hasMore">
			<button class="load-more-btn" @click="handleLoadMore" :loading="loading">
				{{ loading ? '加载中...' : '加载更多' }}
			</button>
		</view>
	</view>
</template>

<script setup>
// 定义props
const props = defineProps({
	transactionList: {
		type: Array,
		default: () => []
	},
	loading: {
		type: Boolean,
		default: false
	},
	hasMore: {
		type: Boolean,
		default: true
	}
})

// 定义emits
const emit = defineEmits(['loadMore'])

// 加载更多
const handleLoadMore = () => {
	emit('loadMore')
}

// 格式化时间显示
const formatTime = (timeStr) => {
	if (!timeStr) return ''
	// 显示年-月-日 时:分
	const date = new Date(timeStr)
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	const day = String(date.getDate()).padStart(2, '0')
	const hours = String(date.getHours()).padStart(2, '0')
	const minutes = String(date.getMinutes()).padStart(2, '0')
	return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 跳转到详情页
const goToDetail = (item) => {
	// 将交易记录数据存储到本地，供详情页使用
	uni.setStorageSync('transactionDetail', item)

	uni.navigateTo({
		url: '/pages/transactionRecord/detail'
	})
}
</script>

<style lang="scss" scoped>
@import url('./index.min.css');


</style>
