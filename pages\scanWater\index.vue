<template>
	<view class="content">
		<!-- 顶部状态栏 -->
		<view class="status-bar">
			<view class="status-card">
				<view class="device-info">
					<view class="device-icon">
						<text class="iconfont">&#xe600;</text>
					</view>
					<view class="device-details">
						<view class="device-name">{{ device.deviceName }}</view>
						<view class="device-status"
							:class="device.appletConState == 'TRUE' ? 'status-busy' : 'status-idle'">
							<view class="status-dot"></view>
							{{ device.appletConState == 'TRUE' ? '使用中' : '空闲' }}
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="main">
			<!-- 支付阶段 -->
			<view class="payment-card" v-if="status === 1">
				<view class="card-header">
					<view class="step-indicator">
						<view class="step-number">1</view>
						<view class="step-title">预付费支付</view>
					</view>
				</view>
				<view class="payment-amount">
					<view class="amount-label">需预付费</view>
					<view class="amount-value">
						<text class="currency">¥</text>
						<text class="price">{{ device.preTradeAmount }}</text>
					</view>
				</view>
				<view class="payment-notice">
					<view class="notice-icon">
						<text class="iconfont">&#xe656;</text>
					</view>
					<view class="notice-text">
						此设备需预付费，使用结束后，未使用完的预付费将返回至余额。用水期间请勿退出当前页面或小程序！
					</view>
				</view>
			</view>
			<!-- 用水阶段 -->
			<view class="usage-card" v-if="status === 2">
				<view class="card-header">
					<view class="step-indicator">
						<view class="step-number active">2</view>
						<view class="step-title">正在用水</view>
					</view>
				</view>
				<view class="payment-status">
					<view class="status-icon">
						<text class="iconfont">&#xe63c;</text>
					</view>
					<view class="status-text">已支付 ¥{{ device.preTradeAmount }}</view>
				</view>
				<view class="device-card">
					<view class="device-item">
						<view class="item-label">设备名称</view>
						<view class="item-value">{{ device.deviceName }}</view>
					</view>
					<view class="device-item">
						<view class="item-label">设备类型</view>
						<view class="item-value">{{ device.deviceTypeName }}</view>
					</view>
					<view class="device-item">
						<view class="item-label">设备编号</view>
						<view class="item-value">{{ device.deviceNo }}</view>
					</view>
					<view class="device-item">
						<view class="item-label">设备型号</view>
						<view class="item-value">{{ device.deviceModel }}</view>
					</view>
					<view class="device-item" v-if="device.areaName">
						<view class="item-label">所在区域</view>
						<view class="item-value">{{ device.areaName }}</view>
					</view>
					<view class="device-item">
						<view class="item-label">在线状态</view>
						<view class="item-value"
							:class="device.deviceOnline === 'TRUE' ? 'status-online' : 'status-offline'">
							{{ device.deviceOnline === 'TRUE' ? '在线' : '离线' }}
						</view>
					</view>
				</view>
				<view class="usage-warning">
					<view class="warning-icon">
						<text class="iconfont">&#xe60c;</text>
					</view>
					<view class="warning-text">
						正在用水中，请勿退出当前页面或小程序！退出后将无法连接到设备。
					</view>
				</view>
			</view>
			<!-- 完成阶段 -->
			<view class="complete-card" v-if="status === 3">
				<view class="card-header">
					<view class="step-indicator">
						<view class="step-number complete">3</view>
						<view class="step-title">消费完成</view>
					</view>
				</view>
				<view class="complete-icon">
					<text class="iconfont">&#xe63c;</text>
				</view>
				<view class="consumption-details">
					<view class="detail-item">
						<view class="detail-label">预扣金额</view>
						<view class="detail-value">¥{{ device.preTradeAmount }}</view>
					</view>
					<view class="detail-item highlight">
						<view class="detail-label">实际消费</view>
						<view class="detail-value">¥{{ orderDetails.tradeAmount }}</view>
					</view>
					<view class="detail-item">
						<view class="detail-label">消费设备</view>
						<view class="detail-value">{{ device.deviceName }}</view>
					</view>
					<view class="detail-item">
						<view class="detail-label">消费时间</view>
						<view class="detail-value">{{ orderDetails.createTime }}</view>
					</view>
				</view>
				<view class="complete-notice">
					<view class="notice-icon">
						<text class="iconfont">&#xe656;</text>
					</view>
					<view class="notice-text">
						消费金额请以消费记录中的金额为准
					</view>
				</view>
			</view>

			<!-- 底部操作按钮 -->
			<view class="action-bar">
				<button v-if="status === 1 && deviceId" class="action-btn payment-btn" @click="handlePay">
					<text class="btn-text">立即支付</text>
				</button>
				<button v-if="status === 2" class="action-btn usage-btn" @click="handleEnd">
					<text class="btn-text">结束用水</text>
				</button>
				<button v-if="status === 3" class="action-btn complete-btn" @click="handleConfirm()">
					<text class="btn-text">确认完成</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad, onUnload, onHide } from '@dcloudio/uni-app'
import { getDeviceInfoBySN, wireLessWaterPay, cutWater, updateConnectState } from '../../api/scanWater.js'
import CONFIG from '../../config.js'


// 响应式数据
const status = ref(1)
const deviceSn = ref(null)
const deviceNo = ref(null)
const deviceId = ref(null)
const orderNo = ref("")

// 设备数据（从接口获取）
const device = ref({
	deviceName: "",
	deviceTypeName: "",
	preTradeAmount: 0,
	appletConState: "FALSE",
	deviceNo: "",
	deviceSn: "",
	deviceId: "",
	deviceModel: "",
	deviceFactory: "",
	deviceStatus: "DEVICE_NORMAL",
	areaName: "",
	merchantName: "",
	deviceOnline: "FALSE",
	deviceType: "WIRELESS_WATER_CONTROLLER"
})

// 订单详情
const orderDetails = ref({
	tradeAmount: "",
	createTime: "",
})

// 页面加载
onLoad(async (options) => {
	console.log('页面参数:', options)

	// 检查是否有sn参数
	if (options.sn) {
		// 使用sn调用接口获取设备信息
		const response = await getDeviceInfoBySN({ deviceSn: options.sn })
		if (response.Status === 1 && response.Data) {
			const deviceData = response.Data
			// 设置设备信息
			device.value = {
				deviceName: deviceData.deviceName || `设备${deviceData.deviceNo}`,
				deviceTypeName: deviceData.deviceTypeName || "",
				preTradeAmount: deviceData.preTradeAmount,
				appletConState: deviceData.appletConState || "FALSE",
				deviceNo: deviceData.deviceNo || "",
				deviceSn: deviceData.deviceSn || "",
				deviceId: deviceData.id || "",
				deviceModel: deviceData.deviceModel || "",
				deviceFactory: deviceData.deviceFactory || "",
				deviceStatus: deviceData.deviceStatus || "DEVICE_NORMAL",
				areaName: deviceData.areaName || "",
				merchantName: deviceData.merchantName || "",
				deviceOnline: deviceData.deviceOnline || "FALSE",
				deviceType: deviceData.deviceType || "WIRELESS_WATER_CONTROLLER"
			}

			// 设置相关变量
			deviceSn.value = deviceData.deviceSn
			deviceNo.value = deviceData.deviceNo
			deviceId.value = deviceData.id
		} else {
			uni.showToast({
				title: response.Message || '获取设备信息失败',
				icon: 'none'
			})
		}
	} else {
		uni.showToast({
			title: '缺少设备SN参数',
			icon: 'none'
		})
	}
})


// 页面隐藏
onHide(() => {
	console.log("页面隐藏")
	// 如果正在使用中，更新设备连接状态
	if (device.value.appletConState === "TRUE") {
		updateDeviceConnectState("FALSE")
		device.value.appletConState = "FALSE"
	}
})

// 页面卸载，如果正在使用中，重置设备状态
onUnload(() => {
	if (device.value.appletConState === "TRUE") {
		// 调用接口更新设备连接状态为FALSE
		updateDeviceConnectState("FALSE")
		device.value.appletConState = "FALSE"
	}
})

// 更新设备连接状态
const updateDeviceConnectState = async (appletConState) => {
	try {
		const params = {
			deviceNo: device.value.deviceNo,
			appletConState: appletConState,
		}

		console.log('更新设备连接状态参数:', params)

		const response = await updateConnectState(params)

		console.log('更新设备连接状态结果:', response)

		if (response.Status === 1) {
			console.log('设备连接状态更新成功')
		} else {
			console.error('设备连接状态更新失败:', response.Message)
		}
	} catch (error) {
		console.error('更新设备连接状态接口调用失败:', error)
	}
}



// 处理支付
const handlePay = async () => {
	// 检查设备状态
	if (device.value.appletConState === "TRUE") {
		return uni.showToast({
			title: "当前设备正在被使用，请稍后再试",
			icon: "none"
		})
	}

	uni.showLoading({
		title: "支付中...",
		mask: true
	})

	try {
		// 调用支付接口
		const payParams = {
			deviceId: device.value.deviceId,
			deviceSn: device.value.deviceSn,
			deviceType: device.value.deviceType || "WIRELESS_WATER_CONTROLLER",
			payAmount: device.value.preTradeAmount,
			tenantId: CONFIG.tenantId,
		}
		const response = await wireLessWaterPay(payParams)
		if (response.Status === 1) {
			// 支付成功
			orderNo.value = response.Data?.orderNo || "ORDER" + Date.now()
			device.value.appletConState = "TRUE"
			status.value = 2

			// 调用接口更新设备连接状态为TRUE
			await updateDeviceConnectState("TRUE")

			uni.hideLoading()
			uni.showToast({
				title: '支付成功，开始用水',
				icon: 'success'
			})
			// 保持屏幕常亮
			uni.setKeepScreenOn({
				keepScreenOn: true
			})
		} else {
			// 支付失败
			uni.hideLoading()
			uni.showToast({
				title: response.Message || '支付失败，请重试',
				icon: 'none',
				complete: () => {
					// 支付失败后返回首页
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/index/index' // 替换为你的 tabBar 页面路径
						});
					}, 1000)
				}
			})
		}

	} catch (error) {
		console.error('支付接口调用失败:', error)
		uni.hideLoading()
		uni.showToast({
			title: '支付失败，请重试',
			icon: 'error',
			complete: () => {
				// 支付异常后返回首页
				setTimeout(() => {
					uni.switchTab({
						url: '/pages/index/index' // 替换为你的 tabBar 页面路径
					});
				}, 1500)
			}
		})
	}
}

// 结束用水
const handleEnd = async () => {
	uni.showLoading({
		title: "结束用水中...",
		mask: true
	})

	try {
		// 调用结束用水接口
		const cutParams = {
			deviceSn: device.value.deviceSn,
			preOrderNo: orderNo.value,
		}

		console.log('结束用水接口请求参数:', cutParams)

		const response = await cutWater(cutParams)

		console.log('结束用水接口返回结果:', response)

		if (response.Status === 1) {
			// 结束用水成功
			const currentTime = new Date()

			orderDetails.value = {
				tradeAmount: response.Data?.tradeAmount || "0.00",
				createTime: response.Data?.createTime || formatTime(currentTime)
			}

			// 重置设备状态
			device.value.appletConState = "FALSE"
			status.value = 3

			// 调用接口更新设备连接状态为FALSE
			await updateDeviceConnectState("FALSE")

			uni.hideLoading()
			uni.showToast({
				title: '用水结束',
				icon: 'success'
			})

			// 取消屏幕常亮
			uni.setKeepScreenOn({
				keepScreenOn: false
			})
		} else {
			// 结束用水失败
			uni.hideLoading()
			uni.showToast({
				title: response.Message || '结束用水失败',
				icon: 'none'
			})
		}

	} catch (error) {
		console.error('结束用水接口调用失败:', error)
		uni.hideLoading()
		uni.showToast({
			title: '结束用水失败',
			icon: 'error'
		})
	}
}

// 确认完成
const handleConfirm = () => {
	uni.navigateBack()
}

// 格式化时间
const formatTime = (date) => {
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	const day = String(date.getDate()).padStart(2, '0')
	const hours = String(date.getHours()).padStart(2, '0')
	const minutes = String(date.getMinutes()).padStart(2, '0')
	const seconds = String(date.getSeconds()).padStart(2, '0')

	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
</script>

<style lang="scss" scoped>
@import url('./index.min.css');

/* 新增状态样式 */
.status-online {
	color: #52c41a !important;
	font-weight: 500;
}

.status-offline {
	color: #ff4d4f !important;
	font-weight: 500;
}

.device-item {
	.item-value {

		&.status-online,
		&.status-offline {
			font-size: 28rpx;
		}
	}
}
</style>
