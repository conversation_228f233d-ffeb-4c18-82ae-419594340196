<template>
	<view class="transaction-detail">
		<!-- 交易状态 -->
		<view class="status-section">
			<view class="status-icon" :class="statusClass">
				<text class="icon-text">{{ statusIcon }}</text>
			</view>
			<text class="status-text">{{ statusText }}</text>
			<text class="amount-text" :class="amountClass">
				{{ detail.type === 'recharge' ? '+' : '-' }}¥{{ detail.amount }}
			</text>
		</view>

		<!-- 交易信息 -->
		<view class="info-section">
			<view class="info-title">交易信息</view>
			<view class="info-list">
				<view class="info-item">
					<text class="info-label">交易流水号</text>
					<text class="info-value">{{ detail.transactionId }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">交易时间</text>
					<text class="info-value">{{ detail.transactionTime }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">交易类型</text>
					<text class="info-value">{{ detail.typeName }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">设备编号</text>
					<text class="info-value">{{ detail.deviceNumber }}</text>
				</view>
				<view class="info-item" v-if="detail.deviceName">
					<text class="info-label">设备名称</text>
					<text class="info-value">{{ detail.deviceName }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">交易后余额</text>
					<text class="info-value">¥{{ detail.balance }}</text>
				</view>
			</view>
		</view>

		<!-- 用户信息 -->
		<view class="info-section" v-if="detail.userName">
			<view class="info-title">用户信息</view>
			<view class="info-list">
				<view class="info-item">
					<text class="info-label">用户姓名</text>
					<text class="info-value">{{ detail.userName }}</text>
				</view>
				<view class="info-item" v-if="detail.deptName">
					<text class="info-label">所属部门</text>
					<text class="info-value">{{ detail.deptName }}</text>
				</view>
				<view class="info-item" v-if="detail.areaName">
					<text class="info-label">所在区域</text>
					<text class="info-value">{{ detail.areaName }}</text>
				</view>
			</view>
		</view>

		<!-- 商户信息 -->
		<view class="info-section" v-if="detail.merchantName">
			<view class="info-title">商户信息</view>
			<view class="info-list">
				<view class="info-item">
					<text class="info-label">商户名称</text>
					<text class="info-value">{{ detail.merchantName }}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<!-- <button class="action-btn" @click="copyTransactionId">复制流水号</button> -->
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 交易详情数据
const detail = ref({})

// 状态相关计算属性
const statusClass = computed(() => {
	if (detail.value.orderStatus === 'END') return 'status-success'
	return 'status-pending'
})

const statusIcon = computed(() => {
	if (detail.value.orderStatus === 'END') return '✓'
	return '⏳'
})

const statusText = computed(() => {
	if (detail.value.orderStatus === 'END') return '交易成功'
	return detail.value.orderStatus || '处理中'
})

const amountClass = computed(() => {
	if (detail.value.type === 'recharge') return 'amount-income'
	if (detail.value.type === 'refund') return 'amount-refund'
	return 'amount-expense'
})

// 页面加载时获取数据
onMounted(() => {
	try {
		const data = uni.getStorageSync('transactionDetail')
		if (data) {
			detail.value = data
			console.log('交易详情数据:', data)
		} else {
			uni.showToast({
				title: '数据获取失败',
				icon: 'none'
			})
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		}
	} catch (error) {
		console.error('获取交易详情失败:', error)
		uni.showToast({
			title: '数据获取失败',
			icon: 'none'
		})
	}
})

// 返回上一页
const goBack = () => {
	uni.navigateBack()
}

// 复制交易流水号
const copyTransactionId = () => {
	uni.setClipboardData({
		data: detail.value.transactionId,
		success: () => {
			uni.showToast({
				title: '流水号已复制',
				icon: 'success'
			})
		}
	})
}
</script>

<style lang="scss" scoped>
@import url('./index.min.css');
</style>
