<script setup>
	import {
		onMounted,
		ref
	} from 'vue';
	import {
		onLoad
	} from "@dcloudio/uni-app"
	import {
		getUserByCode
	} from "@/api/login.js"

	const code = ref(null)

	onLoad((options) => {
		options.userCode = 'o2dY06TkpO0ZX52U51-5DpPq78Wk'
		if (options.userCode) {
			getUserInfo(options.userCode)
		} else if (options.sn) {
			if (uni.getStorageSync('userInfo')) {
				uni.redirectTo({
					url: "/pages/scanWater/index?sn=" + options.sn
				})
			} else {
				window.location.href =
					'http://wap.xt.beescrm.com/charging/common/charging/chargingId/28/portId/44449'
			}
		} else {
			if (!uni.getStorageSync('userInfo')) {
				window.location.href =
					'http://wap.xt.beescrm.com/charging/common/charging/chargingId/28/portId/44449'
			} else {
				uni.redirectTo({
					url: "/pages/index/index"
				})
			}

		}
	})

	const auth = () => {
		if (!uni.getStorageSync('userInfo')) {

		} else {

		}
	}
	const getUserInfo = async (userCode) => {
		let {
			data,
			code
		} = await getUserByCode({
			userCode
		}) //status:1未认证 2待审核 3审核通过 4审核不通过; role:角色; balance:账户余额; discount:折扣; identity_id:身份id
		if (code == 200 && data && data.status == 3) {
			if (data && data.status == 3) {
				uni.setStorageSync("userInfo", data)
				uni.redirectTo({
					url: "/pages/index/index"
				})
			}
		} else {
			//跳转川师微生活授权认证
			window.location.href = 'http://wap.xt.beescrm.com/charging/common/charging/chargingId/28/portId/44449'
		}
	}

	/* const getCode = () => {
		code.value = getUrlCode().code // 截取code
		if (code.value == null || code.value === '') {
			window.location.href = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" +
				'wxa97806e0ac0e9c08' +
				"&redirect_uri=" + encodeURIComponent("http://nsk.ykt.cdcardzn.com") +
				"&response_type=code&scope=snsapi_base#wechat_redirect"
		} else {
			console.log("拿到code获取openid");
		}
	}
	const getUrlCode = () => { // 截取url中的code方法
		var url = location.search
		var theRequest = new Object()
		if (url.indexOf("?") != -1) {
			var str = url.substr(1)
			var strs = str.split("&")
			for (var i = 0; i < strs.length; i++) {
				theRequest[strs[i].split("=")[0]] = (strs[i].split("=")[1])
			}
		}
		return theRequest
	} */
	onMounted(() => {
		// getCode()
	})
</script>