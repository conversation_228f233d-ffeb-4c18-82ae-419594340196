<template>
	<!-- 故障报修表单 -->
	<view class="form-content">
		<view class="device-scan">
			<view class="left">
				<view class="label">
					故障设备
				</view>
				<view class="tip">
					{{reportForm.deviceSn || "当前未获取故障设备"}}
				</view>
			</view>
			<view class="right" @click="handleScan()">
				<view class="tip">
					扫码获取故障设备
				</view>
				<text class="scan-icon">📷</text>
			</view>
		</view>

		<view class="form-item">
			<text class="label">设备名称：</text>
			<uni-easyinput v-model="reportForm.deviceName" disabled></uni-easyinput>
		</view>

		<view class="form-item">
			<text class="label">设备机号：</text>
			<uni-easyinput v-model="reportForm.deviceNo" disabled></uni-easyinput>
		</view>

		<view class="form-item">
			<text class="label">设备位置：</text>
			<uni-easyinput v-model="reportForm.devicePosition" placeholder="请输入设备位置"></uni-easyinput>
		</view>

		<view class="form-item">
			<text class="label">联系电话：</text>
			<uni-easyinput v-model="reportForm.userTel" type="number" placeholder="请输入联系电话" maxlength="11"></uni-easyinput>
		</view>
		<view class="form-item">
			<view class="label">
				选择故障情况
			</view>
			<view class="value">
				<uni-data-select v-model="reportForm.failureType" :localdata="typeList"></uni-data-select>
			</view>
		</view>

		<view class="form-item">
			<text class="label">问题描述：</text>
			<textarea class="textarea" v-model="reportForm.failureDetail" placeholder="请详细描述遇到的问题"
				maxlength="200"></textarea>
		</view>
		<view class="button-group">
			<button class="cancel-btn" @click="goBack">取消</button>
			<button class="submit-btn" @click="submitReport">提交报修</button>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		reactive,
		onMounted,
		getCurrentInstance
	} from 'vue'
	import {
		deviceInfoBySN,
		equipmentFailureTypeList,
		deviceRepairManagementAdd
	} from "@/api/repairReport.js"

	import wx from "weixin-js-sdk";

	const {
		proxy
	} = getCurrentInstance()

	const emit = defineEmits(['success'])
	const typeList = ref([])

	// 报修表单数据
	const reportForm = reactive({
		deviceSn: "",
		deviceName: '',
		deviceType: '',
		deviceNo: '',
		devicePosition: '',
	})
	const getTypeList = async () => {
		let {
			Data,
			Status
		} = await equipmentFailureTypeList({
			deviceType: "WIRELESS_WATER_CONTROLLER"
		})
		if (Status == 1) {
			typeList.value = Data.map(item => {
				return {
					text: item.failureName,
					value: item.failureName
				}
			})
		}
	}
	const getDeviceDetails = async () => {
		let {
			Data,
			Status
		} = await deviceInfoBySN({
			deviceSn: "CD2198000055"
		})
		if (Status == 1) {
			reportForm.deviceType = Data.deviceType
			reportForm.deviceNo = Data.deviceNo
			reportForm.devicePosition = Data.devicePosition
			reportForm.deviceName = Data.deviceName
			reportForm.deviceSn = Data.deviceSn
		}
	}
	// 扫码获取设备信息
	const handleScan = () => {
		return getDeviceDetails()
		wx.scanQRCode({
			needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
			success: function(res) {
				console.log('条码类型：' + res.scanType);
				console.log('条码内容：' + res.result);
				if (res.scanType == "QR_CODE") {
					uni.showLoading({
						title: "加载中...",
						mask: true
					})
					try {

					} catch (error) {
						uni.hideLoading()
						uni.showToast({
							title: "获取设备信息失败",
							icon: "none"
						})
					}
				} else {
					uni.showToast({
						title: "识别码非二维码！",
						icon: "none"
					})
				}
			},
			fail: function(response) {
				uni.showToast({
					title: "识别二维码失败！",
					icon: "none"
				})
			},
		});
	}

	// 提交报修
	const submitReport = async () => {
		// 验证表单
		if (!reportForm.deviceSn) return proxy.$modal.msg('请扫码获取设备信息')
		
		if (!reportForm.failureType) return proxy.$modal.msg('请选择故障类型')

		if (!reportForm.userTel.trim()) return proxy.$modal.msg('请填写联系电话')

		if (!/^1[3-9]\d{9}$/.test(reportForm.userTel)) return proxy.$modal.msg('请填写正确的手机号码')


		// 提交报修
		proxy.$modal.loading('提交中...')
		let params = {
			...reportForm,
		}
		let {
			Status,
			Msg
		} = await deviceRepairManagementAdd(params)
		proxy.$modal.closeLoading()
		if (Status === 1) {
			emit("success")
		}
	}
	// 返回首页
	const goBack = () => {
		uni.navigateBack()
	}
	onMounted(() => {
		getTypeList()
	})
</script>

<style lang="scss" scoped>
	@import url('./index.scss');
</style>