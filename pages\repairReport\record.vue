<template>
	<view class="records-content">
		<view v-if="repairRecords.length === 0" class="empty-state">
			<text class="empty-text">暂无报修记录</text>
		</view>
		<view v-else class="records-list">
			<view v-for="record in repairRecords" :key="record.id" class="record-item">
				<view class="record-header">
					<text class="record-id">报修单号：{{ record.id }}</text>
					<view class="status-badge" :style="{background:getStatusText(record.status).color }">
						{{ getStatusText(record.status).label }}
					</view>
				</view>
				<view class="record-info">
					<text class="info-item">故障设备：{{ record.deviceName  }}</text>
					<text class="info-item">设备类型：{{ record.deviceTypeName }}</text>
					<text class="info-item">设备机号：{{ record.deviceNo  }}</text>
					<text class="info-item">报修时间：{{ record.createTime }}</text>
					<text class="info-item">问题描述：{{ record.failureDetail||'未填写' }}</text>
					<text class="info-item" v-if="record.status=='REPAIRED'">处理时间：{{ record.lastModifyTime }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		reactive,
		onMounted
	} from 'vue'
	import {
		personRepairList
	} from "@/api/repairReport.js"
	// 响应式数据
	const activeTab = ref('report')

	// 报修记录数据（模拟数据）
	const repairRecords = ref([])

	const dormRepairStatus = [{
			label: "已维修",
			value: "REPAIRED",
			color: "#66bb6a"
		},
		{
			label: "未维修",
			value: "NOT_REPAIRED",
			color: "#ffa726"
		},
	];
	const getStatusText = (val) => {
		return dormRepairStatus.find(item => item.value == val) || {}
	}

	const getList = async () => {
		let {
			Status,
			Data
		} = await personRepairList({})
		if (Status === 1) {
			repairRecords.value = Data
		}
	}

	onMounted(() => {
		getList()
	})
</script>

<style lang="scss" scoped>
	@import url('./index.scss');
</style>