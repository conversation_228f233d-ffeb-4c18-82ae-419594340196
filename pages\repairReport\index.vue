<template>
	<view class="repair-report">
		<view class="tab-bar">
			<view class="tab-item" :class="{ active: activeTab === 'report' }" @click="switchTab('report')">
				故障报修
			</view>
			<view class="tab-item" :class="{ active: activeTab === 'records' }" @click="switchTab('records')">
				报修记录
			</view>
		</view>
		<!-- 故障报修表单 -->
		<addRecord v-if="activeTab === 'report'" @success="submitSuccess" />
		<!-- 报修记录列表 -->
		<record v-if="activeTab === 'records'" />
	</view>
</template>
<script setup>
	import {
		ref,
		reactive
	} from 'vue'
	import addRecord from './addRecord.vue';
	import record from "./record.vue"
	// 响应式数据
	const activeTab = ref('report')

	// 切换标签页
	const switchTab = (tab) => {
		activeTab.value = tab
	}
	const submitSuccess = () => {
		activeTab.value = "records"
	}
</script>

<style lang="scss" scoped>
	@import url('./index.scss');
</style>