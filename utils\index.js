export function makeTree(data, key, pid, child) {
	let parents = data.filter(p => p[pid] == 0),
		children = data.filter(c => c[pid] != 0);
	dataToTree(parents, children);
	return parents;

	function dataToTree(parents, children) {
		parents.map(p => {
			children.map((c, i) => {
				if (c[pid] == p[key]) {
					let _children = JSON.parse(JSON.stringify(children));
					_children.splice(i, 1);
					dataToTree([c], _children);
					if (p[child]) {
						p[child].push(c);
					} else {
						p[child] = [c];
					}
				}
			});
		});
	}
}

export function objToUrlQuery(obj) {
	let arr = []
	for (let key in obj) {
		arr.push(`${key}=${obj[key]}`)
	}
	return arr.join("&")
}

