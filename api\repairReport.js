import request from "../utils/request.js"

//根据SN查询设备信息
export function deviceInfoBySN(param) {
	return request('/eccard-school/school/wireLessWater/deviceInfoBySN', param, "post", 'json')
}

//新增设备保修
export function deviceRepairManagementAdd(param) {
	return request('/eccard-school/school/wireLessWater/deviceRepairManagement/insert', param, "post", 'json')
}

//查询故障类型列表
export function equipmentFailureTypeList(param) {
	return request('/eccard-school/school/wireLessWater/equipmentFailureTypeList', param, "post", 'json')
}

//查询保修记录
export function personRepairList(param) {
	return request('/eccard-school/school/wireLessWater/personRepairList', param, "post", 'json')
}