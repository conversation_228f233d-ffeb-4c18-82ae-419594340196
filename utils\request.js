import CONFIG from "../config.js"
let header = {
	'content-type': 'application/x-www-form-urlencoded',
}

const request = (url, data, method, json, userCode = true) => {
	if (json) {
		header = {
			'content-type': 'application/json'
		}
	} else {
		header = {
			'content-type': 'application/x-www-form-urlencoded',
		}
	}
	if (userCode) {
		data.userCode = uni.getStorageSync("userInfo").user_id
	}
	return new Promise((reslove, reject) => {
		uni.request({
			url: CONFIG.baseURL + url,
			data: {
				...data,
				tenantNo: CONFIG.tenantNo,
			},
			method,
			header,
			success: res => {
				console.log(url, data, res);
				if (res.statusCode !== 200) {
					reject(res.data.error)
					return uni.showToast({
						title: res.data.error,
						icon: 'error'
					})
				}
				if (res.data && res.data.Status == -1) {
					uni.showToast({
						title: res.data.message || '接口异常',
						icon: "none"
					})
				}
				reslove(res.data)
			},
			fail: err => {
				console.log(err)
				reject(err.data)
				console.log(url + "接口访问失败了哦")
			}
		});
	})
}

export default request