.content {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  box-sizing: border-box;
}

.status-bar {
  margin-bottom: 30rpx;
}

.status-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.device-info {
  display: flex;
  align-items: center;
}

.device-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.device-icon .iconfont {
  font-size: 36rpx;
  color: #ffffff;
}

.device-details {
  flex: 1;
}

.device-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.device-status {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.device-status.status-idle {
  color: #52c41a;
}

.device-status.status-busy {
  color: #ff4d4f;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-idle .status-dot {
  background: #52c41a;
}

.status-busy .status-dot {
  background: #ff4d4f;
}

.main {
  padding-bottom: 120rpx;
}

.payment-card, .usage-card, .complete-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.step-indicator {
  display: flex;
  align-items: center;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #e8e8e8;
  color: #999999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  margin-right: 20rpx;
}

.step-number.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.step-number.complete {
  background: #52c41a;
  color: #ffffff;
}

.step-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.payment-amount {
  padding: 40rpx 30rpx;
  text-align: center;
}

.amount-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

.amount-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.amount-value .currency {
  font-size: 36rpx;
  color: #ff6b35;
  margin-right: 8rpx;
}

.amount-value .price {
  font-size: 72rpx;
  font-weight: 600;
  color: #ff6b35;
}

.payment-status {
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(82, 196, 26, 0.1);
}

.payment-status .status-icon {
  margin-right: 12rpx;
}

.payment-status .status-icon .iconfont {
  font-size: 32rpx;
  color: #52c41a;
}

.payment-status .status-text {
  font-size: 30rpx;
  color: #52c41a;
  font-weight: 600;
}

.device-card {
  padding: 30rpx;
  margin: 20rpx 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.device-item:not(:last-child) {
  border-bottom: 1rpx solid #e8e8e8;
}

.item-label {
  font-size: 28rpx;
  color: #666666;
}

.item-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.complete-icon {
  padding: 40rpx;
  text-align: center;
}

.complete-icon .iconfont {
  font-size: 120rpx;
  color: #52c41a;
}

.consumption-details {
  padding: 0 30rpx 30rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item.highlight {
  background: rgba(255, 107, 53, 0.1);
  margin: 0 -20rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  border-bottom: none;
}

.detail-item.highlight .detail-value {
  color: #ff6b35;
  font-weight: 600;
}

.detail-label {
  font-size: 28rpx;
  color: #666666;
}

.detail-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.payment-notice, .usage-warning, .complete-notice {
  padding: 24rpx 30rpx;
  margin: 20rpx 30rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
}

.payment-notice, .complete-notice {
  background: rgba(24, 144, 255, 0.1);
  border-left: 6rpx solid #1890ff;
}

.payment-notice .notice-icon .iconfont, .complete-notice .notice-icon .iconfont {
  color: #1890ff;
}

.usage-warning {
  background: rgba(255, 77, 79, 0.1);
  border-left: 6rpx solid #ff4d4f;
}

.usage-warning .warning-icon .iconfont {
  color: #ff4d4f;
}

.notice-icon, .warning-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.notice-icon .iconfont, .warning-icon .iconfont {
  font-size: 32rpx;
  line-height: 1;
}

.notice-text, .warning-text {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.6;
  color: #666666;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  transition: all 0.3s ease;
}

.action-btn.payment-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 53, 0.3);
}

.action-btn.usage-btn {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  box-shadow: 0 8rpx 20rpx rgba(255, 77, 79, 0.3);
}

.action-btn.complete-btn {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  box-shadow: 0 8rpx 20rpx rgba(82, 196, 26, 0.3);
}

.action-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.btn-icon {
  font-family: 'iconfont';
  font-size: 32rpx;
  margin-right: 12rpx;
}

.btn-text {
  font-size: 32rpx;
}

.btn-icon {
  font-family: 'iconfont';
  font-size: 32rpx;
  margin-right: 12rpx;
}

.btn-text {
  font-size: 32rpx;
}
