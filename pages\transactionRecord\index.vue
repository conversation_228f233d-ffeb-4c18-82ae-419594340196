<template>
	<view class="transaction-record">
		<!-- 搜索栏组件 -->
		<SearchBar
			v-model="searchParams.deviceNumber"
			@search="handleSearch"
			@filter="showFilterModal = true"
		/>

		<!-- 筛选弹窗组件 -->
		<FilterModal
			v-model:visible="showFilterModal"
			:filterParams="filterParams"
			@confirm="handleFilterConfirm"
			@reset="handleFilterReset"
		/>

		<!-- 交易记录列表组件 -->
		<List
			:transactionList="transactionList"
			:loading="loading"
			:hasMore="hasMore"
			@loadMore="loadMore"
		/>
	</view>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import SearchBar from './SearchBar.vue'
import FilterModal from './FilterModal.vue'
import List from './list.vue'
import { getPersonalTradeList } from '@/api/transactionRecord.js'

// 响应式数据
const searchParams = ref({
	deviceNumber: ''
})

// 筛选参数
const filterParams = ref({
	deviceNumber: '',
	startDate: '',
	endDate: ''
})

// 分页参数
const pageParams = ref({
	currentPage: 1,
	pageSize: 10
})

// 弹窗显示状态
const showFilterModal = ref(false)

// 监听搜索参数中的设备编号变化，同步到筛选参数
watch(() => searchParams.value.deviceNumber, (newVal) => {
	filterParams.value.deviceNumber = newVal
})

// 监听筛选参数中的设备编号变化，同步到搜索参数
watch(() => filterParams.value.deviceNumber, (newVal) => {
	searchParams.value.deviceNumber = newVal
})

const transactionList = ref([])

const loading = ref(false)
const hasMore = ref(true)

// 处理搜索
const handleSearch = (searchValue) => {
	filterParams.value.deviceNumber = searchValue
	console.log('搜索设备编号:', searchValue)
	searchTransactions()
}

// 处理筛选确认
const handleFilterConfirm = (params) => {
	console.log('筛选条件:', params)
	// 更新筛选参数
	filterParams.value = { ...params }
	// 同步设备编号到搜索参数
	searchParams.value.deviceNumber = params.deviceNumber
	// 执行搜索
	searchTransactions()
}

// 处理筛选重置
const handleFilterReset = () => {
	filterParams.value = {
		deviceNumber: '',
		startDate: '',
		endDate: ''
	}
	searchParams.value.deviceNumber = ''
}



// 查询交易记录
const searchTransactions = async () => {
	try {
		uni.showLoading({
			title: '查询中...'
		})
		console.log('筛选接口', filterParams.value);

		// 重置分页
		pageParams.value.currentPage = 1

		// 调用实际的API查询交易记录
		await loadTransactionList(true)

		uni.hideLoading()
		uni.showToast({
			title: '查询完成',
			icon: 'success'
		})
	} catch (error) {
		console.error('查询交易记录失败:', error)
		uni.hideLoading()
		uni.showToast({
			title: '查询失败，请重试',
			icon: 'none'
		})
	}
}



// 加载交易记录列表
const loadTransactionList = async (isRefresh = false) => {
	try {
		// 构建请求参数
		const params = {
			currentPage: pageParams.value.currentPage,
			pageSize: pageParams.value.pageSize,
		}

		// 添加筛选条件
		if (filterParams.value.deviceNumber) {
			params.deviceNo = filterParams.value.deviceNumber
		}
		if (filterParams.value.startDate) {
			// 开始时间设为当天的0点0分0秒
			params.startTime = filterParams.value.startDate + ' 00:00:00'
			console.log('开始时间:', params.startTime)
		}
		if (filterParams.value.endDate) {
			// 结束时间设为当天的23点59分59秒
			params.endTime = filterParams.value.endDate + ' 23:59:59'
			console.log('结束时间:', params.endTime)
		}

		console.log('请求参数:', params)

		// 调用交易记录接口
		const response = await getPersonalTradeList(params)

		console.log('API响应:', response)

		if (response && response.Status === 1) {
			const data = response.Data || {}
			const list = data.list || []
			const total = data.total || 0
			const hasNextPage = data.hasNextPage || false

			console.log(`本次加载数据条数: ${list.length}, 总数据条数: ${total}, 是否有下一页: ${hasNextPage}`)

			// 转换数据格式以适配页面显示
			const formattedList = list.map(item => ({
				transactionId: item.tradeNo,
				deviceNumber: item.deviceNo, // 使用deviceNo字段
				deviceName: item.deviceName, // 保留设备名称
				transactionTime: item.createTime,
				type: item.tradeAmount > 0 ? 'consume' : 'refund',
				typeName: item.tradeModeName,
				amount: Math.abs(item.tradeAmount).toFixed(2),
				waterVolume: '', // 接口中没有用水量字段
				balance: item.tradePreBalance ? item.tradePreBalance.toFixed(2) : '0.00',
				userName: item.userName,
				deptName: item.deptName,
				areaName: item.areaName,
				merchantName: item.merchantName,
				orderStatus: item.orderStatus,
				originalData: item // 保留原始数据
			}))

			if (isRefresh) {
				// 刷新数据
				transactionList.value = formattedList
				console.log(`刷新数据完成，当前总条数: ${transactionList.value.length}`)
			} else {
				// 加载更多数据
				const beforeCount = transactionList.value.length
				transactionList.value = [...transactionList.value, ...formattedList]
				console.log(`加载更多完成，之前: ${beforeCount}条，新增: ${formattedList.length}条，当前总条数: ${transactionList.value.length}`)
			}

			// 更新是否还有更多数据 - 使用接口返回的hasNextPage字段
			hasMore.value = hasNextPage
			console.log(`是否还有更多数据: ${hasMore.value}, 当前已加载: ${transactionList.value.length}/${total}`)
		} else {
			throw new Error(response?.message || '获取交易记录失败')
		}
	} catch (error) {
		console.error('加载交易记录失败:', error)
		throw error
	}
}

// 加载更多
const loadMore = async () => {
	if (loading.value || !hasMore.value) return

	loading.value = true

	try {
		// 增加页码
		pageParams.value.currentPage++

		// 加载更多数据
		await loadTransactionList(false)

		loading.value = false
	} catch (error) {
		console.error('加载更多失败:', error)
		loading.value = false

		// 恢复页码
		pageParams.value.currentPage--

		uni.showToast({
			title: '加载失败，请重试',
			icon: 'none'
		})
	}
}

// 生命周期钩子
onMounted(async () => {
	try {
		await loadTransactionList(true)
	} catch (error) {
		console.error('初始化加载失败:', error)
	}
})
</script>

<style lang="scss" scoped>
@import url('./index.min.css');


</style>
