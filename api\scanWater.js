import request from '../utils/request.js'

/**
 * 根据SN查询设备信息——扫码调用
 */
export function getDeviceInfoBySN(params) {
    return request('/eccard-school/school/wireLessWater/deviceInfoBySN', params, 'POST', true)
}



/**
 * 支付交易接口——确认支付
 */
export function wireLessWaterPay(params) {
    return request('/eccard-school/school/wireLessWater/pay', params, 'POST', true)
}

/**
 * 结束用水接口——结束用水
 */
export function cutWater(params) {
    return request('/eccard-school/school/wireLessWater/pay', params, 'POST', true)
}

/**
 * 更新设备连接状态接口
 */
export function updateConnectState(params) {
    return request('/eccard-school/school/wireLessWater/updateConnectState', params, 'POST', true)
}
