<script>
	import wx from "weixin-js-sdk";
	import {
		wxJsSdkData
	} from "@/api/login.js"
	export default {
		onLaunch: function() {
			// this.setWxJsSdk()
		},
		onShow: function() {},
		onHide: function() {},
		methods: {
			async setWxJsSdk() {
				console.log(location);
				let {
					Data,
					Status
				} = await wxJsSdkData({
					url: location.href
				})
				if (Status == 1) {
					wx.config({
						debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
						appId: Data.appid, // 必填，公众号的唯一标识
						timestamp: Data.timestamp, // 必填，生成签名的时间戳
						nonceStr: Data.noncestr, // 必填，生成签名的随机串
						signature: Data.sign, // 必填，签名
						jsApiList: ["scanQRCode"] // 必填，需要使用的JS接口列表
					});
				} else {
					uni.showToast({
						title: "初始化微信jssdk失败",
						icon: "none"
					})
				}

			}
		}
	}
</script>

<style>
	@import url("./static/icon/iconfont.css");
	/*每个页面公共css */
</style>