<template>
	<view class="user-info">
		<view class="info-section">
			<!-- 用户基本信息 -->
			<view class="info-card">
				<view class="card-header">
					<text class="iconfont">&#xe796;</text>
					<text class="card-title">用户基本信息</text>
				</view>
				<view class="info-content">
					<view class="info-item">
						<text class="label">用户ID：</text>
						<text class="value">{{ userInfo.userId }}</text>
					</view>
					<view class="info-item">
						<text class="label">用户编码：</text>
						<text class="value">{{ userInfo.userCode }}</text>
					</view>
					<view class="info-item">
						<text class="label">用户名：</text>
						<text class="value">{{ userInfo.userName }}</text>
					</view>
					<view class="info-item">
						<text class="label">学校名称：</text>
						<text class="value">{{ userInfo.schoolName }}</text>
					</view>
					<view class="info-item" v-if="userInfo.facultyName">
						<text class="label">院系名称：</text>
						<text class="value">{{ userInfo.facultyName }}</text>
					</view>
					<view class="info-item" v-if="userInfo.gradeName">
						<text class="label">年级：</text>
						<text class="value">{{ userInfo.gradeName }}</text>
					</view>
					<view class="info-item" v-if="userInfo.className">
						<text class="label">班级：</text>
						<text class="value">{{ userInfo.className }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getUserInfoByCode } from '../../api/userInfo.js'

// 响应式数据
const userInfo = ref({
	userId: '',
	userName: '',
	headImg: '',
	schoolName: '',
	facultyName: '',
	gradeName: '',
	className: '',
})


// 加载用户信息
const loadUserInfo = async () => {
	try {
		uni.showLoading({
			title: '加载中...'
		})
		const result = await getUserInfoByCode({})
		uni.hideLoading()
		// 如果接口返回成功，更新页面数据
		if (result && result.Status === 1 && result.Data) {
			// 根据接口返回的数据结构更新 userInfo
			userInfo.value = {
				...userInfo.value,
				userId: result.Data.userId || '',
				userName: result.Data.userName || '',
				headImg: result.Data.headImg || '',
				schoolName: result.Data.schoolName || '',
				facultyName: result.Data.facultyName || '',
				gradeName: result.Data.gradeName || '',
				className: result.Data.className || ''
			}
		} else {
			uni.showToast({
				title: result?.Message || '获取用户信息失败',
				icon: 'none'
			})
		}

	} catch (error) {
		uni.hideLoading()
		uni.showToast({
			title: '加载失败，请重试',
			icon: 'none'
		})
	}
}



// 生命周期钩子
onMounted(() => {
	loadUserInfo()
})
</script>
<style lang="scss" scoped>

@import url('./index.min.css');

</style>
